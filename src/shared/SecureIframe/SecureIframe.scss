.secure-iframe-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .secure-iframe {
    width: 100%;
    height: 100%;
    border: none;
    transition: opacity 0.3s ease-in-out;
    
    &.loading {
      opacity: 0;
    }
    
    &.error {
      display: none;
    }
  }
  
  .secure-iframe-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    z-index: 1;
    
    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-message {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }
  
  .secure-iframe-error {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    z-index: 1;
    padding: 20px;
    text-align: center;
    
    .error-icon {
      font-size: 48px;
    }
    
    .error-message {
      font-size: 16px;
      color: #dc3545;
      font-weight: 500;
      max-width: 300px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .secure-iframe-container {
    .secure-iframe-loading {
      .loading-spinner {
        width: 24px;
        height: 24px;
        border-width: 2px;
      }
      
      .loading-message {
        font-size: 12px;
      }
    }
    
    .secure-iframe-error {
      padding: 16px;
      
      .error-icon {
        font-size: 36px;
      }
      
      .error-message {
        font-size: 14px;
      }
    }
  }
}
