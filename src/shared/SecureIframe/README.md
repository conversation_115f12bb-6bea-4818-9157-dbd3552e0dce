# SecureIframe Component

A reusable, secure iframe component with built-in loading states, error handling, and security features.

## Features

- 🔒 **Secure by default** - Uses restrictive sandbox permissions
- 📱 **Responsive** - Adapts to different screen sizes
- ⏳ **Loading states** - Built-in loading indicator
- ❌ **Error handling** - Graceful error display
- 🎨 **Customizable** - Flexible styling and configuration
- ♿ **Accessible** - Proper ARIA attributes and semantic HTML
- 🧪 **Well tested** - Comprehensive test coverage

## Basic Usage

```tsx
import { SecureIframe } from 'src/shared/SecureIframe';

function MyComponent() {
  return (
    <SecureIframe
      src="https://example.com"
      title="Example Website"
    />
  );
}
```

## Advanced Usage

```tsx
import { SecureIframe } from 'src/shared/SecureIframe';

function AnalyticsComponent() {
  const handleLoad = () => {
    console.log('Analytics loaded successfully');
  };

  const handleError = () => {
    console.error('Failed to load analytics');
  };

  return (
    <SecureIframe
      src="https://analytics.example.com"
      title="Analytics Dashboard"
      className="analytics-iframe"
      width="100%"
      height="600px"
      allowFullScreen
      sandbox="allow-same-origin allow-scripts allow-forms"
      onLoad={handleLoad}
      onError={handleError}
      loadingMessage="Loading analytics dashboard..."
      errorMessage="Unable to load analytics. Please check your connection."
      testId="analytics-iframe"
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `src` | `string` | **required** | The URL to load in the iframe |
| `title` | `string` | **required** | Title for accessibility |
| `className` | `string` | `''` | Custom CSS class name |
| `style` | `CSSProperties` | `{}` | Custom inline styles |
| `width` | `string \| number` | `'100%'` | Width of the iframe |
| `height` | `string \| number` | `'100%'` | Height of the iframe |
| `allowFullScreen` | `boolean` | `false` | Whether to allow fullscreen |
| `sandbox` | `string` | `'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads'` | Custom sandbox permissions |
| `loading` | `'lazy' \| 'eager'` | `'lazy'` | Loading behavior |
| `onLoad` | `() => void` | `undefined` | Callback when iframe loads |
| `onError` | `() => void` | `undefined` | Callback when iframe fails to load |
| `showLoadingIndicator` | `boolean` | `true` | Whether to show loading indicator |
| `loadingMessage` | `string` | `'Loading...'` | Custom loading message |
| `showErrorMessage` | `boolean` | `true` | Whether to show error message |
| `errorMessage` | `string` | `'Failed to load content. Please try again.'` | Custom error message |
| `testId` | `string` | `'secure-iframe'` | Test ID for testing |

## Security Features

The component includes several security features by default:

- **Sandbox restrictions**: Limits what the iframe can do
- **No border**: Prevents clickjacking indicators
- **Controlled permissions**: Only allows necessary capabilities

### Default Sandbox Permissions

- `allow-same-origin`: Allows content to be treated as from its origin
- `allow-scripts`: Allows JavaScript execution
- `allow-forms`: Allows form submission
- `allow-popups`: Allows popups
- `allow-top-navigation`: Allows navigation of top-level context
- `allow-downloads`: Allows downloads

You can customize these by passing a custom `sandbox` prop.

## Styling

The component comes with default styles that can be customized:

```scss
.secure-iframe-container {
  // Container styles
}

.secure-iframe {
  // Iframe styles
}

.secure-iframe-loading {
  // Loading indicator styles
}

.secure-iframe-error {
  // Error message styles
}
```

## Testing

The component includes comprehensive tests. Run them with:

```bash
npm test SecureIframe.test.tsx
```

## Migration from Analytics Component

If you're migrating from the Analytics component:

**Before:**
```tsx
<iframe
  src={analyticsUrl}
  className="analyticsIframe"
  title="Superset Analytics Panel"
  allowFullScreen
  sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads"
  style={{ border: 'none' }}
/>
```

**After:**
```tsx
<SecureIframe
  src={analyticsUrl}
  className="analyticsIframe"
  title="Superset Analytics Panel"
  allowFullScreen
/>
```
