import React from 'react';
import { SecureIframe } from './index';

// Basic usage example
export const BasicExample = () => {
  return (
    <SecureIframe
      src="https://example.com"
      title="Example Website"
    />
  );
};

// Advanced usage example
export const AdvancedExample = () => {
  const handleLoad = () => {
    console.log('Iframe loaded successfully');
  };

  const handleError = () => {
    console.error('Failed to load iframe');
  };

  return (
    <SecureIframe
      src="https://analytics.example.com"
      title="Analytics Dashboard"
      className="my-custom-iframe"
      width="100%"
      height="600px"
      allowFullScreen={true}
      sandbox="allow-same-origin allow-scripts allow-forms"
      onLoad={handleLoad}
      onError={handleError}
      loadingMessage="Loading analytics dashboard..."
      errorMessage="Unable to load analytics. Please check your connection."
      testId="analytics-iframe"
      style={{ borderRadius: '8px' }}
    />
  );
};

// Usage in Analytics component (replacement example)
export const AnalyticsExample = ({ analyticsUrl }) => {
  return (
    <div className="analyticsContainer" data-testid="analytics-container">
      <SecureIframe
        src={analyticsUrl}
        className="analyticsIframe"
        title="Superset Analytics Panel"
        allowFullScreen={true}
      />
    </div>
  );
};
