import React, { useCallback, useState } from 'react';
import './SecureIframe.scss';

const SecureIframe = ({
  src,
  title,
  className = '',
  style = {},
  width = '100%',
  height = '100%',
  allowFullScreen = false,
  sandbox = 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads',
  loading = 'lazy',
  onLoad,
  onError,
  showLoadingIndicator = true,
  loadingMessage = 'Loading...',
  showErrorMessage = true,
  errorMessage = 'Failed to load content. Please try again.',
  testId = 'secure-iframe',
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  }, [onError]);

  const iframeStyle = {
    border: 'none',
    width,
    height,
    ...style
  };

  return (
    <div 
      className={`secure-iframe-container ${className}`}
      data-testid={`${testId}-container`}
    >
      {isLoading && showLoadingIndicator && (
        <div className="secure-iframe-loading" data-testid={`${testId}-loading`}>
          <div className="loading-spinner"></div>
          <span className="loading-message">{loadingMessage}</span>
        </div>
      )}
      
      {hasError && showErrorMessage && (
        <div className="secure-iframe-error" data-testid={`${testId}-error`}>
          <div className="error-icon">⚠️</div>
          <span className="error-message">{errorMessage}</span>
        </div>
      )}
      
      <iframe
        src={src}
        title={title}
        className={`secure-iframe ${isLoading ? 'loading' : ''} ${hasError ? 'error' : ''}`}
        style={iframeStyle}
        sandbox={sandbox}
        allowFullScreen={allowFullScreen}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        data-testid={testId}
      />
    </div>
  );
};

export default SecureIframe;
