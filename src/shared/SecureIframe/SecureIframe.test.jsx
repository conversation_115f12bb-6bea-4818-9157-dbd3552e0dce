import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SecureIframe from './SecureIframe';

describe('SecureIframe', () => {
  const defaultProps = {
    src: 'https://example.com',
    title: 'Test Iframe'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders iframe with correct attributes', () => {
    render(<SecureIframe {...defaultProps} />);
    
    const iframe = screen.getByTestId('secure-iframe');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', 'https://example.com');
    expect(iframe).toHaveAttribute('title', 'Test Iframe');
    expect(iframe).toHaveAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads');
  });

  it('shows loading indicator by default', () => {
    render(<SecureIframe {...defaultProps} />);
    
    expect(screen.getByTestId('secure-iframe-loading')).toBeInTheDocument();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('hides loading indicator when iframe loads', async () => {
    render(<SecureIframe {...defaultProps} />);
    
    const iframe = screen.getByTestId('secure-iframe');
    fireEvent.load(iframe);
    
    await waitFor(() => {
      expect(screen.queryByTestId('secure-iframe-loading')).not.toBeInTheDocument();
    });
  });

  it('shows error message when iframe fails to load', async () => {
    render(<SecureIframe {...defaultProps} />);
    
    const iframe = screen.getByTestId('secure-iframe');
    fireEvent.error(iframe);
    
    await waitFor(() => {
      expect(screen.getByTestId('secure-iframe-error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load content. Please try again.')).toBeInTheDocument();
    });
  });

  it('calls onLoad callback when iframe loads', async () => {
    const onLoad = jest.fn();
    render(<SecureIframe {...defaultProps} onLoad={onLoad} />);
    
    const iframe = screen.getByTestId('secure-iframe');
    fireEvent.load(iframe);
    
    await waitFor(() => {
      expect(onLoad).toHaveBeenCalledTimes(1);
    });
  });

  it('calls onError callback when iframe fails', async () => {
    const onError = jest.fn();
    render(<SecureIframe {...defaultProps} onError={onError} />);
    
    const iframe = screen.getByTestId('secure-iframe');
    fireEvent.error(iframe);
    
    await waitFor(() => {
      expect(onError).toHaveBeenCalledTimes(1);
    });
  });

  it('applies custom className and styles', () => {
    const customStyle = { backgroundColor: 'red' };
    render(
      <SecureIframe 
        {...defaultProps} 
        className="custom-class" 
        style={customStyle}
      />
    );
    
    const container = screen.getByTestId('secure-iframe-container');
    expect(container).toHaveClass('secure-iframe-container', 'custom-class');
    
    const iframe = screen.getByTestId('secure-iframe');
    expect(iframe).toHaveStyle('background-color: red');
  });

  it('supports custom sandbox permissions', () => {
    render(
      <SecureIframe 
        {...defaultProps} 
        sandbox="allow-scripts allow-same-origin"
      />
    );
    
    const iframe = screen.getByTestId('secure-iframe');
    expect(iframe).toHaveAttribute('sandbox', 'allow-scripts allow-same-origin');
  });

  it('supports allowFullScreen attribute', () => {
    render(<SecureIframe {...defaultProps} allowFullScreen />);
    
    const iframe = screen.getByTestId('secure-iframe');
    expect(iframe).toHaveAttribute('allowfullscreen');
  });

  it('can disable loading and error indicators', () => {
    render(
      <SecureIframe 
        {...defaultProps} 
        showLoadingIndicator={false}
        showErrorMessage={false}
      />
    );
    
    expect(screen.queryByTestId('secure-iframe-loading')).not.toBeInTheDocument();
    
    const iframe = screen.getByTestId('secure-iframe');
    fireEvent.error(iframe);
    
    expect(screen.queryByTestId('secure-iframe-error')).not.toBeInTheDocument();
  });

  it('supports custom loading and error messages', () => {
    render(
      <SecureIframe 
        {...defaultProps} 
        loadingMessage="Custom loading..."
        errorMessage="Custom error message"
      />
    );
    
    expect(screen.getByText('Custom loading...')).toBeInTheDocument();
    
    const iframe = screen.getByTestId('secure-iframe');
    fireEvent.error(iframe);
    
    expect(screen.getByText('Custom error message')).toBeInTheDocument();
  });

  it('supports custom testId', () => {
    render(<SecureIframe {...defaultProps} testId="custom-iframe" />);
    
    expect(screen.getByTestId('custom-iframe')).toBeInTheDocument();
    expect(screen.getByTestId('custom-iframe-container')).toBeInTheDocument();
  });
});
