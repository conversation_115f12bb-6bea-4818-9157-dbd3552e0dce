import React, { FC } from 'react';
import { useAppContext } from 'AppContextProvider';
import './Analytics.scss';

const Analytics: FC = () => {
  const { appConfig } = useAppContext();
  const analyticsUrl = (appConfig?.analyticsUrl as string);

  return (
    <div className="analyticsContainer" data-testid="analytics-container">
      <iframe
        src={analyticsUrl}
        className="analyticsIframe"
        title="Superset Analytics Panel"
        allowFullScreen
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads"
        style={{ border: 'none' }}
      />
    </div>
  );
};

export default Analytics;
